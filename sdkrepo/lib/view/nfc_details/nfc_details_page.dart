import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class NFCDetailsPage extends GetView<DashboardController> {
  const NFCDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: "NFC Details", backallow: true),
      body: GetBuilder<DashboardController>(
        id: 'page_update',
        builder: (_) {
          if (controller.sessiondata == null) {
            return const Center(
              child: CupertinoActivityIndicator(),
            );
          }

          final nfcData = controller.sessiondata!.nfc;
          final nfcImages = controller.sessiondata!.nfcImage;

          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SpaceH24(),

                  // NFC Image Section
                  if (nfcImages.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(bottom: 24),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        gradient: const LinearGradient(
                          end: Alignment.topLeft,
                          begin: Alignment.bottomRight,
                          colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                        ),
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(38),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "NFC Document Image",
                              style: context.headlineSmall.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                            const SpaceH16(),
                            GestureDetector(
                              onTap: () => Get.toNamed(
                                Routes.PHOTOVIEW,
                                arguments: nfcImages[0].documentPath,
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.1),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: ImageService.image(
                                  nfcImages[0].documentPath,
                                  borderRadius: 12.0,
                                  imageHeight: 200,
                                  imageWidth: double.maxFinite,
                                ),
                              ),
                            ),
                            const SpaceH12(),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.7),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    size: 16,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    "Tap image to view full size",
                                    style: context.bodySmall.copyWith(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // NFC Data Section
                  if (nfcData?.isNotEmpty == true)
                    Container(
                      margin: const EdgeInsets.only(bottom: 24),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        gradient: const LinearGradient(
                          end: Alignment.topLeft,
                          begin: Alignment.bottomRight,
                          colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                        ),
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(38),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "NFC Data",
                              style: context.headlineSmall.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                            const SpaceH16(),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.8),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.5),
                                ),
                              ),
                              child: Text(
                                nfcData!,
                                style: context.bodyLarge.copyWith(
                                  height: 1.5,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Empty state if no data
                  if ((nfcData?.isEmpty ?? true) && nfcImages.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.grey[200]!,
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.nfc_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SpaceH16(),
                          Text(
                            "No NFC Data Available",
                            style: context.titleLarge.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SpaceH8(),
                          Text(
                            "NFC verification has not been completed yet.",
                            textAlign: TextAlign.center,
                            style: context.bodyMedium.copyWith(
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ),

                  const SpaceH32(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
