import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class NFCDetailsPage extends GetView<DashboardController> {
  const NFCDetailsPage({super.key});

  Map<String, dynamic>? _parseNfcData(String? nfcData) {
    if (nfcData == null || nfcData.isEmpty) return null;
    try {
      return json.decode(nfcData) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  String _formatGender(String? gender) {
    if (gender == null || gender.isEmpty) return 'Not available';
    switch (gender.toUpperCase()) {
      case 'M':
        return 'Male';
      case 'F':
        return 'Female';
      case 'X':
        return 'Unspecified';
      default:
        return gender;
    }
  }

  Widget _buildDataField(BuildContext context, String label, String? value, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            label,
            style: context.bodySmall.copyWith(
              color: Colors.black54,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          if (value == null || value.isEmpty) ...[
            Text(
              'Not available',
              style: context.bodyLarge.copyWith(
                color: Colors.grey[500],
                fontWeight: FontWeight.w600,
              ),
            ),
          ] else ...[
            Text(
              value,
              style: context.bodyLarge.copyWith(
                color: Colors.black87,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: "NFC Details", backallow: true),
      body: GetBuilder<DashboardController>(
        id: 'page_update',
        builder: (_) {
          if (controller.sessiondata == null) {
            return const Center(
              child: CupertinoActivityIndicator(),
            );
          }

          final nfcData = controller.sessiondata!.nfc;
          final nfcImages = controller.sessiondata!.nfcImage;

          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SpaceH32(),

                  // NFC Image Section
                  if (nfcImages.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 20, bottom: 4),
                          child: Text(
                            "FACE IMAGE",
                            style: context.bodyMedium.copyWith(
                              fontWeight: FontWeight.w300,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: 24),
                          clipBehavior: Clip.antiAlias,
                          decoration: ShapeDecoration(
                            color: const Color(0xffE9EFF1),
                            shape: ContinuousRectangleBorder(
                              borderRadius: BorderRadius.circular(24),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: GestureDetector(
                              onTap: () => Get.toNamed(
                                Routes.PHOTOVIEW,
                                arguments: nfcImages[0].documentPath,
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  children: [
                                    ImageService.image(
                                      nfcImages[0].documentPath,
                                      borderRadius: 0,
                                      // i need to the height must be width of the screen /2
                                      imageHeight: context.deviceWidth / 2,
                                      imageWidth: context.deviceWidth / 2,
                                    ),
                                    const Spacer(),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                  // NFC Data Section
                  if (nfcData?.isNotEmpty == true)
                    Container(
                      margin: const EdgeInsets.only(bottom: 24),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        color: const Color(0xffE9EFF1),
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "NFC Data",
                              style: context.headlineSmall.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                            const SpaceH16(),
                            Builder(
                              builder: (context) {
                                final parsedData = _parseNfcData(nfcData);
                                if (parsedData != null) {
                                  return Column(
                                    children: [
                                      // Document Information
                                      if (parsedData['Document Type'] != null)
                                        _buildDataField(
                                          context,
                                          "Document Type",
                                          parsedData['Document Type']?.toString(),
                                          Icons.description,
                                        ),
                                      if (parsedData['Document Number'] != null)
                                        _buildDataField(
                                          context,
                                          "Document Number",
                                          parsedData['Document Number']?.toString(),
                                          Icons.credit_card,
                                        ),

                                      // Personal Information
                                      if (parsedData['Name'] != null)
                                        _buildDataField(
                                          context,
                                          "Name",
                                          parsedData['Name']?.toString(),
                                          Icons.person,
                                        ),
                                      if (parsedData['Surname'] != null && parsedData['Surname'].toString().isNotEmpty)
                                        _buildDataField(
                                          context,
                                          "Surname",
                                          parsedData['Surname']?.toString(),
                                          Icons.person_outline,
                                        ),
                                      if (parsedData['Holder Name'] != null)
                                        _buildDataField(
                                          context,
                                          "Holder Name (Arabic)",
                                          parsedData['Holder Name']?.toString(),
                                          Icons.person_pin,
                                        ),
                                      if (parsedData['Other Names'] != null && parsedData['Other Names'].toString().isNotEmpty)
                                        _buildDataField(
                                          context,
                                          "Other Names",
                                          parsedData['Other Names']?.toString(),
                                          Icons.people,
                                        ),

                                      // Personal Details
                                      if (parsedData['Gender'] != null)
                                        _buildDataField(
                                          context,
                                          "Gender",
                                          _formatGender(parsedData['Gender']?.toString()),
                                          Icons.wc,
                                        ),
                                      if (parsedData['Date of Birth'] != null)
                                        _buildDataField(
                                          context,
                                          "Date of Birth",
                                          parsedData['Date of Birth']?.toString(),
                                          Icons.cake,
                                        ),
                                      if (parsedData['placeOfBirth'] != null)
                                        _buildDataField(
                                          context,
                                          "Place of Birth",
                                          parsedData['placeOfBirth']?.toString(),
                                          Icons.location_city,
                                        ),

                                      // Country Information
                                      if (parsedData['Country'] != null)
                                        _buildDataField(
                                          context,
                                          "Country",
                                          parsedData['Country']?.toString(),
                                          Icons.flag,
                                        ),
                                      if (parsedData['Nationality'] != null)
                                        _buildDataField(
                                          context,
                                          "Nationality",
                                          parsedData['Nationality']?.toString(),
                                          Icons.public,
                                        ),

                                      // Document Validity
                                      if (parsedData['Date of Expiry'] != null)
                                        _buildDataField(
                                          context,
                                          "Date of Expiry",
                                          parsedData['Date of Expiry']?.toString(),
                                          Icons.event,
                                        ),

                                      // Additional Information
                                      if (parsedData['personalNumber'] != null)
                                        _buildDataField(
                                          context,
                                          "Personal Number",
                                          parsedData['personalNumber']?.toString(),
                                          Icons.badge,
                                        ),
                                      if (parsedData['Additional data 1'] != null && parsedData['Additional data 1'].toString().isNotEmpty)
                                        _buildDataField(
                                          context,
                                          "Additional Data 1",
                                          parsedData['Additional data 1']?.toString(),
                                          Icons.info,
                                        ),
                                      if (parsedData['Additional data 2'] != null && parsedData['Additional data 2'].toString().isNotEmpty)
                                        _buildDataField(
                                          context,
                                          "Additional Data 2",
                                          parsedData['Additional data 2']?.toString(),
                                          Icons.info_outline,
                                        ),
                                    ],
                                  );
                                } else {
                                  // Fallback to raw data display if parsing fails
                                  return Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.8),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.white.withValues(alpha: 0.5),
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.info_outline,
                                              size: 16,
                                              color: Colors.orange[600],
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              "Raw NFC Data",
                                              style: context.bodySmall.copyWith(
                                                color: Colors.orange[600],
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          nfcData!,
                                          style: context.bodyMedium.copyWith(
                                            height: 1.5,
                                            color: Colors.black87,
                                            fontFamily: 'monospace',
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Empty state if no data
                  if ((nfcData?.isEmpty ?? true) && nfcImages.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.grey[200]!,
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.nfc_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SpaceH16(),
                          Text(
                            "No NFC Data Available",
                            style: context.titleLarge.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SpaceH8(),
                          Text(
                            "NFC verification has not been completed yet.",
                            textAlign: TextAlign.center,
                            style: context.bodyMedium.copyWith(
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ),

                  const SpaceH32(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
