import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';

class NFCDetailsPage extends GetView<DashboardController> {
  const NFCDetailsPage({super.key});

  Map<String, dynamic>? _parseNfcData(String? nfcData) {
    if (nfcData == null || nfcData.isEmpty) return null;
    try {
      return json.decode(nfcData) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return 'Not available';
    try {
      // Try to parse common date formats
      if (dateString.length == 6) {
        // YYMMDD format
        final year = int.parse(dateString.substring(0, 2));
        final month = int.parse(dateString.substring(2, 4));
        final day = int.parse(dateString.substring(4, 6));
        final fullYear = year < 50 ? 2000 + year : 1900 + year;
        return '$day/${month.toString().padLeft(2, '0')}/$fullYear';
      } else if (dateString.length == 8) {
        // YYYYMMDD format
        final year = dateString.substring(0, 4);
        final month = dateString.substring(4, 6);
        final day = dateString.substring(6, 8);
        return '$day/$month/$year';
      }
      return dateString;
    } catch (e) {
      return dateString;
    }
  }

  String _formatGender(String? gender) {
    if (gender == null || gender.isEmpty) return 'Not available';
    switch (gender.toUpperCase()) {
      case 'M':
        return 'Male';
      case 'F':
        return 'Female';
      case 'X':
        return 'Unspecified';
      default:
        return gender;
    }
  }

  Widget _buildDataField(BuildContext context, String label, String? value, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.5),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: context.bodySmall.copyWith(
                    color: Colors.black54,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value ?? 'Not available',
                  style: context.bodyLarge.copyWith(
                    color: value != null ? Colors.black87 : Colors.grey[500],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: "NFC Details", backallow: true),
      body: GetBuilder<DashboardController>(
        id: 'page_update',
        builder: (_) {
          if (controller.sessiondata == null) {
            return const Center(
              child: CupertinoActivityIndicator(),
            );
          }

          final nfcData = controller.sessiondata!.nfc;
          final nfcImages = controller.sessiondata!.nfcImage;

          print(nfcData);

          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SpaceH24(),

                  // NFC Image Section
                  if (nfcImages.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(bottom: 24),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        gradient: const LinearGradient(
                          end: Alignment.topLeft,
                          begin: Alignment.bottomRight,
                          colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                        ),
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(38),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "NFC Document Image",
                              style: context.headlineSmall.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                            const SpaceH16(),
                            GestureDetector(
                              onTap: () => Get.toNamed(
                                Routes.PHOTOVIEW,
                                arguments: nfcImages[0].documentPath,
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.1),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: ImageService.image(
                                  nfcImages[0].documentPath,
                                  borderRadius: 12.0,
                                  imageHeight: 200,
                                  imageWidth: double.maxFinite,
                                ),
                              ),
                            ),
                            const SpaceH12(),
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 16,
                                  color: Colors.grey[300],
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  "Tap image to view full size",
                                  style: context.bodySmall.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                  // NFC Data Section
                  if (nfcData?.isNotEmpty == true)
                    Container(
                      margin: const EdgeInsets.only(bottom: 24),
                      clipBehavior: Clip.antiAlias,
                      decoration: ShapeDecoration(
                        gradient: const LinearGradient(
                          end: Alignment.topLeft,
                          begin: Alignment.bottomRight,
                          colors: [Color(0xffD7C0FB), Color(0xffEEE3FF)],
                        ),
                        shape: ContinuousRectangleBorder(
                          borderRadius: BorderRadius.circular(38),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "NFC Data",
                              style: context.headlineSmall.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                            const SpaceH16(),
                            Builder(
                              builder: (context) {
                                final parsedData = _parseNfcData(nfcData);
                                if (parsedData != null) {
                                  return Column(
                                    children: [
                                      _buildDataField(
                                        context,
                                        "Holder Name",
                                        parsedData['Holder Name']?.toString(),
                                        Icons.person,
                                      ),
                                      _buildDataField(
                                        context,
                                        "Other Names",
                                        parsedData['Other Names']?.toString(),
                                        Icons.person,
                                      ),
                                      _buildDataField(
                                        context,
                                        "placeOfBirth",
                                        parsedData['placeOfBirth']?.toString(),
                                        Icons.person,
                                      ),
                                      _buildDataField(
                                        context,
                                        "personalNumber",
                                        parsedData['personalNumber']?.toString(),
                                        Icons.person,
                                      ),
                                      _buildDataField(
                                        context,
                                        "Name",
                                        parsedData['Name']?.toString(),
                                        Icons.person,
                                      ),
                                      _buildDataField(
                                        context,
                                        "Document Type",
                                        _formatGender(parsedData['Document Type']?.toString()),
                                        Icons.wc,
                                      ),
                                      _buildDataField(
                                        context,
                                        "Document Number",
                                        parsedData['Document Number']?.toString(),
                                        Icons.flag,
                                      ),
                                      _buildDataField(
                                        context,
                                        "Country",
                                        _formatDate(parsedData['Country']?.toString()),
                                        Icons.cake,
                                      ),
                                      _buildDataField(
                                        context,
                                        "Nationality",
                                        _formatDate(parsedData['Nationality']?.toString()),
                                        Icons.event,
                                      ),
                                      _buildDataField(
                                        context,
                                        "Surname",
                                        parsedData['Surname']?.toString(),
                                        Icons.credit_card,
                                      ),
                                      if (parsedData['nationality'] != null)
                                        _buildDataField(
                                          context,
                                          "Nationality",
                                          parsedData['nationality']?.toString(),
                                          Icons.public,
                                        ),
                                      if (parsedData['documentType'] != null)
                                        _buildDataField(
                                          context,
                                          "Document Type",
                                          parsedData['documentType']?.toString(),
                                          Icons.description,
                                        ),
                                      if (parsedData['surname'] != null)
                                        _buildDataField(
                                          context,
                                          "Surname",
                                          parsedData['surname']?.toString(),
                                          Icons.person_outline,
                                        ),
                                      if (parsedData['issuingCountry'] != null)
                                        _buildDataField(
                                          context,
                                          "Issuing Country",
                                          parsedData['issuingCountry']?.toString(),
                                          Icons.location_on,
                                        ),
                                      if (parsedData['personalNumber'] != null)
                                        _buildDataField(
                                          context,
                                          "Personal Number",
                                          parsedData['personalNumber']?.toString(),
                                          Icons.badge,
                                        ),
                                    ],
                                  );
                                } else {
                                  // Fallback to raw data display if parsing fails
                                  return Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(alpha: 0.8),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: Colors.white.withValues(alpha: 0.5),
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.info_outline,
                                              size: 16,
                                              color: Colors.orange[600],
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              "Raw NFC Data",
                                              style: context.bodySmall.copyWith(
                                                color: Colors.orange[600],
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 12),
                                        Text(
                                          nfcData!,
                                          style: context.bodyMedium.copyWith(
                                            height: 1.5,
                                            color: Colors.black87,
                                            fontFamily: 'monospace',
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Empty state if no data
                  if ((nfcData?.isEmpty ?? true) && nfcImages.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.grey[200]!,
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.nfc_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SpaceH16(),
                          Text(
                            "No NFC Data Available",
                            style: context.titleLarge.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SpaceH8(),
                          Text(
                            "NFC verification has not been completed yet.",
                            textAlign: TextAlign.center,
                            style: context.bodyMedium.copyWith(
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ),

                  const SpaceH32(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
