import 'package:flutter/cupertino.dart';

import '../../resources/exports/index.dart';
import 'package:signature/signature.dart';

class SignatureScreen extends GetView<SignController> {
  const SignatureScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const CustomAppBar(title: Strings.SIGNATURE, backallow: true),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          const SizedBox(),
          GetBuilder<SignController>(
            id: 'signature',
            builder: (_) => Signature(
              key: const Key('signature'),
              controller: controller.signatureCtrl,
              height: 300,
              backgroundColor: Colors.grey[300]!,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: controller.clearSignature,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 11),
                    width: double.maxFinite,
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      // color: const Color(0xff8240DE),
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                        side: BorderSide(color: Color(0xff8240DE)),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: const DefaultTextStyle(
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      child: Text(
                        "Clear",
                        style: TextStyle(
                          color: Color(0xff8240DE),
                        ),
                      ),
                    ),
                  ),
                ).expanded(),
                // onTap: controller.exportImage,
                const SpaceW16(),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: controller.exportImage,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    width: double.maxFinite,
                    clipBehavior: Clip.antiAlias,
                    decoration: ShapeDecoration(
                      color: const Color(0xff8240DE),
                      shape: ContinuousRectangleBorder(
                        borderRadius: BorderRadius.circular(24.0),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: const DefaultTextStyle(
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      child: Text("Done"),
                    ),
                  ),
                ).expanded(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
